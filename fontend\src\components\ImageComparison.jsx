import React from 'react';

const ImageComparison = ({ originalImage, compressedImage, compressionData }) => {
    const handleDownload = async () => {
        try {
            const response = await fetch(`http://localhost:3000${compressedImage.url}`);
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `compressed-${Date.now()}.jpg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Download failed:', error);
            alert('Failed to download image');
        }
    };

    if (!originalImage || !compressedImage) {
        return null;
    }

    return (
        <div className="w-full max-w-6xl mx-auto mt-8">
            <h2 className="text-2xl font-bold text-center mb-6">Compression Results</h2>
            
            {/* Compression Stats */}
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div className="bg-white rounded-lg p-4 shadow-sm">
                        <h3 className="text-lg font-semibold text-gray-700">Original Size</h3>
                        <p className="text-2xl font-bold text-blue-600">{originalImage.sizeFormatted}</p>
                    </div>
                    <div className="bg-white rounded-lg p-4 shadow-sm">
                        <h3 className="text-lg font-semibold text-gray-700">Compressed Size</h3>
                        <p className="text-2xl font-bold text-green-600">{compressedImage.sizeFormatted}</p>
                    </div>
                    <div className="bg-white rounded-lg p-4 shadow-sm">
                        <h3 className="text-lg font-semibold text-gray-700">Size Reduction</h3>
                        <p className="text-2xl font-bold text-purple-600">{compressionData.compressionRatio}%</p>
                    </div>
                </div>
                
                {compressionData.regionsDetected > 0 && (
                    <div className="mt-4 text-center">
                        <p className="text-sm text-gray-600">
                            🤖 AI detected <span className="font-semibold">{compressionData.regionsDetected}</span> regions of interest
                        </p>
                    </div>
                )}
            </div>

            {/* Image Comparison */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Original Image */}
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="bg-gray-100 px-4 py-2 border-b">
                        <h3 className="font-semibold text-gray-700">Original Image</h3>
                    </div>
                    <div className="p-4">
                        <img
                            src={`http://localhost:3000${originalImage.url}`}
                            alt="Original"
                            className="w-full h-auto rounded-lg shadow-sm"
                        />
                        <div className="mt-3 text-sm text-gray-600">
                            <p>Size: {originalImage.sizeFormatted}</p>
                        </div>
                    </div>
                </div>

                {/* Compressed Image */}
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="bg-gray-100 px-4 py-2 border-b">
                        <h3 className="font-semibold text-gray-700">Compressed Image</h3>
                    </div>
                    <div className="p-4">
                        <img
                            src={`http://localhost:3000${compressedImage.url}`}
                            alt="Compressed"
                            className="w-full h-auto rounded-lg shadow-sm"
                        />
                        <div className="mt-3 text-sm text-gray-600">
                            <p>Size: {compressedImage.sizeFormatted}</p>
                            <p className="text-green-600 font-medium">
                                Saved {compressionData.compressionRatio}% space
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Download Button */}
            <div className="text-center mt-6">
                <button
                    onClick={handleDownload}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors shadow-md"
                >
                    📥 Download Compressed Image
                </button>
            </div>

            {/* Success Message */}
            {compressionData.message && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-green-800 text-center">{compressionData.message}</p>
                </div>
            )}
        </div>
    );
};

export default ImageComparison;
