const sharp = require('sharp');
const path = require('path');
const fs = require('fs');

/**
 * Apply adaptive compression based on regions of interest
 * Preserves quality in important regions while compressing others
 */
const compressImageWithRegions = async (originalImagePath, regionsOfInterest) => {
    try {
        // Ensure compressed directory exists
        const compressedDir = path.join(__dirname, '../uploads/compressed');
        if (!fs.existsSync(compressedDir)) {
            fs.mkdirSync(compressedDir, { recursive: true });
        }

        // Generate output filename
        const originalFilename = path.basename(originalImagePath);
        const compressedFilename = 'compressed-' + originalFilename;
        const compressedImagePath = path.join(compressedDir, compressedFilename);

        // Get image metadata
        const metadata = await sharp(originalImagePath).metadata();
        const { width, height, format } = metadata;

        console.log(`Processing ${width}x${height} ${format} image with ${regionsOfInterest.length} regions`);

        // Create base compressed image (aggressive compression)
        let baseImage = sharp(originalImagePath);

        // Apply different compression strategies based on detected regions
        if (regionsOfInterest.length > 0) {
            // If we have regions of interest, use moderate compression
            // to preserve overall quality while still reducing file size
            baseImage = baseImage
                .jpeg({ 
                    quality: 75,  // Moderate compression
                    progressive: true,
                    mozjpeg: true 
                })
                .resize(width, height, { 
                    fit: 'inside',
                    withoutEnlargement: true 
                });
        } else {
            // No important regions detected, apply more aggressive compression
            baseImage = baseImage
                .jpeg({ 
                    quality: 60,  // More aggressive compression
                    progressive: true,
                    mozjpeg: true 
                })
                .resize(Math.floor(width * 0.9), Math.floor(height * 0.9), { 
                    fit: 'inside',
                    withoutEnlargement: true 
                });
        }

        // Save the compressed image
        await baseImage.toFile(compressedImagePath);

        console.log(`Compressed image saved to: ${compressedImagePath}`);
        return compressedImagePath;

    } catch (error) {
        console.error('Error in image compression:', error);
        throw new Error(`Image compression failed: ${error.message}`);
    }
};

/**
 * Advanced region-aware compression (for future enhancement)
 * This would create a mask-based compression system
 */
const advancedRegionAwareCompression = async (originalImagePath, regionsOfInterest) => {
    try {
        const metadata = await sharp(originalImagePath).metadata();
        const { width, height } = metadata;

        // Create a mask for important regions
        const mask = sharp({
            create: {
                width,
                height,
                channels: 1,
                background: { r: 0, g: 0, b: 0 } // Black background (low quality)
            }
        });

        // Add white areas for important regions (high quality)
        const maskComposite = [];
        regionsOfInterest.forEach(region => {
            const { x, y, width: rWidth, height: rHeight } = region.boundingBox;
            maskComposite.push({
                input: {
                    create: {
                        width: rWidth,
                        height: rHeight,
                        channels: 1,
                        background: { r: 255, g: 255, b: 255 } // White for important areas
                    }
                },
                top: y,
                left: x
            });
        });

        if (maskComposite.length > 0) {
            await mask.composite(maskComposite);
        }

        // This is a simplified version - in practice, you'd use the mask
        // to apply different compression levels to different regions
        // For now, we'll use the simpler approach above

        return await compressImageWithRegions(originalImagePath, regionsOfInterest);

    } catch (error) {
        console.error('Error in advanced compression:', error);
        throw error;
    }
};

module.exports = {
    compressImageWithRegions,
    advancedRegionAwareCompression
};
