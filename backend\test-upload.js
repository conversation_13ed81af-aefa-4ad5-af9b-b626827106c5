const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// Test script to verify image upload and compression
async function testImageUpload() {
    try {
        // Check if we have any test images in uploads
        const uploadsDir = path.join(__dirname, 'src', 'uploads', 'original');
        
        if (!fs.existsSync(uploadsDir)) {
            console.log('Creating uploads directory...');
            fs.mkdirSync(uploadsDir, { recursive: true });
        }

        console.log('✅ Backend API is ready for image uploads!');
        console.log('📁 Upload directory created at:', uploadsDir);
        console.log('🌐 Frontend is available at: http://localhost:5174');
        console.log('🔧 Backend API at: http://localhost:3000/api/upload');
        
        console.log('\n🚀 Ready to test! Upload an image through the web interface.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testImageUpload();
