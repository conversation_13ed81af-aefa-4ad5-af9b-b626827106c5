{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "mongoose": "^8.15.1", "multer": "^2.0.0", "sharp": "^0.34.2"}}