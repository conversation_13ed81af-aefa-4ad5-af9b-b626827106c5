# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
/backend/dist
/frontend/dist
/frontend/build

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Uploaded files (don't commit user uploads)
/backend/src/uploads/original/*
/backend/src/uploads/compressed/*

# Keep upload directories but ignore contents
!/backend/src/uploads/original/.gitkeep
!/backend/src/uploads/compressed/.gitkeep

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Package lock files (choose one)
# package-lock.json
# yarn.lock
