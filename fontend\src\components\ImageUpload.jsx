import React, { useState, useRef } from 'react';

const ImageUpload = ({ onImageSelect, isProcessing }) => {
    const [dragActive, setDragActive] = useState(false);
    const [selectedImage, setSelectedImage] = useState(null);
    const [previewUrl, setPreviewUrl] = useState(null);
    const fileInputRef = useRef(null);

    const handleDrag = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === "dragenter" || e.type === "dragover") {
            setDragActive(true);
        } else if (e.type === "dragleave") {
            setDragActive(false);
        }
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFile(e.dataTransfer.files[0]);
        }
    };

    const handleChange = (e) => {
        e.preventDefault();
        if (e.target.files && e.target.files[0]) {
            handleFile(e.target.files[0]);
        }
    };

    const handleFile = (file) => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file (JPG, PNG, etc.)');
            return;
        }

        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            alert('File size must be less than 10MB');
            return;
        }

        setSelectedImage(file);
        
        // Create preview URL
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
        
        // Notify parent component
        onImageSelect(file);
    };

    const onButtonClick = () => {
        fileInputRef.current?.click();
    };

    const clearSelection = () => {
        setSelectedImage(null);
        setPreviewUrl(null);
        onImageSelect(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    return (
        <div className="w-full max-w-lg mx-auto">
            <div
                className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    dragActive 
                        ? 'border-blue-400 bg-blue-50' 
                        : 'border-gray-300 hover:border-gray-400'
                } ${isProcessing ? 'opacity-50 pointer-events-none' : ''}`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
            >
                <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleChange}
                    disabled={isProcessing}
                />

                {previewUrl ? (
                    <div className="space-y-4">
                        <img
                            src={previewUrl}
                            alt="Preview"
                            className="max-w-full max-h-64 mx-auto rounded-lg shadow-md"
                        />
                        <div className="text-sm text-gray-600">
                            <p className="font-medium">{selectedImage?.name}</p>
                            <p>{(selectedImage?.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                        <button
                            onClick={clearSelection}
                            className="px-4 py-2 text-sm text-red-600 hover:text-red-800 transition-colors"
                            disabled={isProcessing}
                        >
                            Remove Image
                        </button>
                    </div>
                ) : (
                    <div className="space-y-4">
                        <div className="text-6xl text-gray-400">📷</div>
                        <div>
                            <p className="text-lg font-medium text-gray-700">
                                Drop your image here, or{' '}
                                <button
                                    onClick={onButtonClick}
                                    className="text-blue-600 hover:text-blue-800 underline"
                                    disabled={isProcessing}
                                >
                                    browse
                                </button>
                            </p>
                            <p className="text-sm text-gray-500 mt-2">
                                Supports JPG, PNG files up to 10MB
                            </p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ImageUpload;
