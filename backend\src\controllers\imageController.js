const fs = require('fs');
const path = require('path');
const { detectRegionsOfInterest } = require('../services/aiService');
const { compressImageWithRegions } = require('../services/imageProcessingService');

const uploadAndCompressImage = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No image file uploaded' });
        }

        const originalImagePath = req.file.path;
        const originalStats = fs.statSync(originalImagePath);
        const originalSize = originalStats.size;

        console.log('Processing image:', req.file.filename);

        // Step 1: Detect regions of interest using AI (mocked for MVP)
        const regionsOfInterest = await detectRegionsOfInterest(originalImagePath);

        // Step 2: Apply adaptive compression
        const compressedImagePath = await compressImageWithRegions(
            originalImagePath,
            regionsOfInterest
        );

        // Get compressed file stats
        const compressedStats = fs.statSync(compressedImagePath);
        const compressedSize = compressedStats.size;

        // Calculate compression ratio
        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);

        // Generate URLs for frontend
        const originalUrl = `/uploads/original/${req.file.filename}`;
        const compressedUrl = `/uploads/compressed/${path.basename(compressedImagePath)}`;

        res.json({
            success: true,
            originalImage: {
                url: originalUrl,
                size: originalSize,
                sizeFormatted: formatFileSize(originalSize)
            },
            compressedImage: {
                url: compressedUrl,
                size: compressedSize,
                sizeFormatted: formatFileSize(compressedSize)
            },
            compressionRatio: compressionRatio,
            regionsDetected: regionsOfInterest.length,
            message: `Image compressed successfully! Reduced size by ${compressionRatio}%`
        });

    } catch (error) {
        console.error('Error processing image:', error);
        res.status(500).json({ 
            error: 'Failed to process image', 
            details: error.message 
        });
    }
};

// Helper function to format file size
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

module.exports = {
    uploadAndCompressImage
};
