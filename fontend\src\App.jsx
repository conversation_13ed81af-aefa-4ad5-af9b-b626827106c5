import { useState } from 'react';
import axios from 'axios';
import ImageUpload from './components/ImageUpload';
import ImageComparison from './components/ImageComparison';
import './App.css';

function App() {
  const [selectedImage, setSelectedImage] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [compressionResult, setCompressionResult] = useState(null);
  const [error, setError] = useState(null);

  const handleImageSelect = (file) => {
    setSelectedImage(file);
    setCompressionResult(null);
    setError(null);
  };

  const handleCompress = async () => {
    if (!selectedImage) {
      alert('Please select an image first');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('image', selectedImage);

      const response = await axios.post('http://localhost:3000/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setCompressionResult(response.data);
    } catch (error) {
      console.error('Compression failed:', error);
      setError(error.response?.data?.error || 'Failed to compress image');
    } finally {
      setIsProcessing(false);
    }
  };

  const resetApp = () => {
    setSelectedImage(null);
    setCompressionResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            🤖 AI-Powered Image Compression
          </h1>
          <p className="text-lg text-gray-600">
            Upload an image and let AI detect important regions for smart compression
          </p>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {!compressionResult ? (
            <div className="space-y-6">
              {/* Image Upload */}
              <ImageUpload
                onImageSelect={handleImageSelect}
                isProcessing={isProcessing}
              />

              {/* Compress Button */}
              {selectedImage && (
                <div className="text-center">
                  <button
                    onClick={handleCompress}
                    disabled={isProcessing}
                    className={`px-8 py-3 rounded-lg font-semibold text-white transition-colors ${
                      isProcessing
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {isProcessing ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </span>
                    ) : (
                      '🚀 Compress Image'
                    )}
                  </button>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-center">{error}</p>
                </div>
              )}
            </div>
          ) : (
            <div>
              {/* Compression Results */}
              <ImageComparison
                originalImage={compressionResult.originalImage}
                compressedImage={compressionResult.compressedImage}
                compressionData={compressionResult}
              />

              {/* Reset Button */}
              <div className="text-center mt-8">
                <button
                  onClick={resetApp}
                  className="px-6 py-2 text-blue-600 hover:text-blue-800 border border-blue-600 hover:border-blue-800 rounded-lg transition-colors"
                >
                  ← Compress Another Image
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="text-center mt-12 text-gray-500 text-sm">
          <p>Built with React, Node.js, and AI-powered region detection</p>
        </div>
      </div>
    </div>
  );
}

export default App;
